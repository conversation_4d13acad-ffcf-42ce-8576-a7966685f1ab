"use client"

import styles from "./ResetTokenModal.module.scss";
import React, { useState, useEffect, useRef } from 'react';
import Image from "next/image";
import JC_Modal from "../../JC_Modal/JC_Modal";
import <PERSON><PERSON>_<PERSON>ton from "../../JC_Button/JC_Button";
import JC_Field from "../../JC_Field/JC_Field";
import { JC_Utils } from "../../../Utils";
import { LocalStorageKeyEnum } from "../../../enums/LocalStorageKey";
import { FieldTypeEnum } from "@/app/enums/FieldType";

// Constants for YouTube Music API
const YT_MUSIC_URL = 'https://music.youtube.com';

export default function ResetTokenModal({ isOpen, onClose }: { isOpen: boolean, onClose: () => void }) {

    // - STATE - //

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [tokenStatus, setTokenStatus] = useState<'none' | 'pending' | 'success' | 'error'>('none');
    const [manualCookie, setManualCookie] = useState<string>('');
    const [manualAuth, setManualAuth] = useState<string>('');

    // - REFS - //

    const mainContainerRef = useRef<HTMLDivElement>(null);

    // - EFFECTS - //

    // No cleanup needed for new tab approach
    useEffect(() => {
        return () => {
            // Nothing to clean up
        };
    }, []);

    // - FUNCTIONS - //

    // Scroll to bottom of modal
    const scrollToBottom = () => {
        if (mainContainerRef.current) {
            // Use setTimeout to ensure DOM updates are complete
            setTimeout(() => {
                if (mainContainerRef.current) {
                    mainContainerRef.current.scrollTop = mainContainerRef.current.scrollHeight;
                }
            }, 100);
        }
    };

    // Open a URL in a new tab
    const openInNewTab = (url: string) => {
        window.open(url, '_blank');
    };

    // Open YouTube Music in a new tab
    const openYouTubeMusic = () => {
        // Reset state
        setIsLoading(true);
        try {
            // Open YouTube Music in a new tab
            openInNewTab(YT_MUSIC_URL);
            setIsLoading(false);
        } catch (error) {
            console.error("Error opening YouTube Music:", error);
            setIsLoading(false);
            setTokenStatus('error');
            JC_Utils.showToastError("Failed to open YouTube Music. Please try again.");
        }
    };

    // Parse cURL command or fetch() call to extract headers
    const parseCurlCommand = (input: string): { cookie: string; authorization: string } => {
        let cookie = "";
        let authorization = "";

        // Log the input for debugging
        console.log('Parsing input command...');

        // Check if it's a fetch() call (new format) or traditional cURL
        if (input.trim().startsWith('fetch(')) {
            // Parse JavaScript fetch() format
            try {
                // Extract the headers object from the fetch call
                const headersMatch = input.match(/"headers":\s*{([\s\S]*?)}/m);
                if (headersMatch) {
                    const headersString = headersMatch[1];

                    // Extract cookie
                    const cookieMatch = headersString.match(/"cookie":\s*"([^"]+)"/);
                    if (cookieMatch) {
                        cookie = cookieMatch[1];
                        console.log('Found cookie in fetch format');
                    }

                    // Extract authorization
                    const authMatch = headersString.match(/"authorization":\s*"([^"]+)"/);
                    if (authMatch) {
                        authorization = authMatch[1];
                        console.log('Found authorization in fetch format');
                    }
                }
            } catch (error) {
                console.error('Error parsing fetch format:', error);
            }
        } else {
            // Handle traditional cURL format (legacy support)
            const normalizedCommand = input
                .replace(/\\'/g, "'")
                .replace(/\\\\'/g, "'")
                .replace(/\\\\\\'/g, "'")
                .replace(/\\\\\\\\'/g, "'")
                .replace(/\\\\\\\\\\'/g, "'")
                // Handle Windows-style curl commands with ^ escaping
                .replace(/\\^/g, " ^")  // Add space before ^ for easier parsing
                .replace(/\\^%\\^3D/g, "=")  // Replace ^%^3D with =
                .replace(/\\^%\\^/g, "%");  // Replace ^%^ with %

            // Function to find the end of a header value
            const findHeaderEnd = (startPos: number): number => {
                // Common terminators in curl commands
                const terminators = [
                    ' ^"',       // Windows curl with double quotes
                    ' ^\'',      // Windows curl with single quotes
                    ' -H',       // Next header
                    ' --',       // Next option
                    '\n',        // Newline
                    '\r\n'       // Windows newline
                ];

                // Find the position of each terminator
                const positions = terminators.map(term => {
                    const pos = normalizedCommand.indexOf(term, startPos);
                    return pos !== -1 ? pos : Number.MAX_SAFE_INTEGER;
                });

                // Get the earliest terminator position
                const earliestPos = Math.min(...positions);
                return earliestPos !== Number.MAX_SAFE_INTEGER ? earliestPos : normalizedCommand.length;
            };

            // Extract cookie header
            const cookieIndex = normalizedCommand.toLowerCase().indexOf('cookie:');
            if (cookieIndex !== -1) {
                // Find the start of the cookie value (after 'cookie:')
                const cookieStart = cookieIndex + 'cookie:'.length;
                // Find the end of the cookie value
                const cookieEnd = findHeaderEnd(cookieStart);
                // Extract and clean the cookie value
                const cookieValue = normalizedCommand.substring(cookieStart, cookieEnd).trim();
                if (cookieValue) {
                    // Clean up the cookie value (remove quotes and escape characters)
                    cookie = cookieValue.replace(/["'^\\]/g, '');
                    console.log('Found cookie with direct extraction method');
                }
            }

            // Extract authorization header
            const authIndex = normalizedCommand.toLowerCase().indexOf('authorization:');
            if (authIndex !== -1) {
                // Find the start of the auth value (after 'authorization:')
                const authStart = authIndex + 'authorization:'.length;
                // Find the end of the auth value
                const authEnd = findHeaderEnd(authStart);
                // Extract and clean the auth value
                const authValue = normalizedCommand.substring(authStart, authEnd).trim();
                if (authValue) {
                    // Clean up the auth value (remove quotes and escape characters)
                    authorization = authValue.replace(/["'^\\]/g, '');
                    console.log('Found authorization with direct extraction method');
                }
            }
        }

        console.log('Extracted cookie:', cookie ? cookie.substring(0, 30) + '...' : 'none');
        console.log('Extracted authorization:', authorization ? authorization.substring(0, 30) + '...' : 'none');

        return { cookie, authorization };
    };

    // Handle pasting cURL command
    const handlePasteCurl = async () => {
        try {
            const text = await navigator.clipboard.readText();

            // Try to extract headers regardless of format
            const { cookie, authorization } = parseCurlCommand(text);

            let foundHeaders = false;

            if (authorization) {
                setManualAuth(authorization);
                foundHeaders = true;
            }

            if (cookie) {
                // Check if cookie is truncated at "__Secure"
                if (cookie.trim().endsWith("__Secure")) {
                    setManualCookie(cookie);
                    foundHeaders = true;
                    JC_Utils.showToastError("Cookie appears to be truncated. Please check and complete it manually.");
                } else {
                    setManualCookie(cookie);
                    foundHeaders = true;
                }
            }

            if (cookie && authorization) {
            } else if (foundHeaders) {
                if (!authorization) {
                    JC_Utils.showToastError("Could not find Authorization header in the cURL command");
                } else if (!cookie) {
                    JC_Utils.showToastError("Could not find Cookie header in the cURL command");
                } else {
                    JC_Utils.showToastSuccess("Partially extracted headers from clipboard");
                }
            } else {
                // If we couldn't find any headers, check if it's a valid format
                if (text.trim().toLowerCase().includes('curl') || text.trim().startsWith('fetch(')) {
                    JC_Utils.showToastError("Could not find Cookie and Authorization headers in the command");
                } else {
                    // Don't set anything if it doesn't look like a valid command
                    JC_Utils.showToastError("Invalid input. Please paste a valid cURL command or fetch() call");
                }
            }

            // Auto-scroll to bottom if any headers were found
            if (foundHeaders) {
                scrollToBottom();
            }
        } catch (err) {
            JC_Utils.showToastError("Failed to read from clipboard. Please paste manually.");
        }
    };

    // Validate token format
    const validateTokens = () => {
        let isValid = true;
        let errorMessage = "";

        if (!manualAuth) {
            isValid = false;
            errorMessage = "Please enter the Authorization value";
        } else if (!manualAuth.trim().startsWith("SAPISIDHASH")) {
            isValid = false;
            errorMessage = "Authorization should start with 'SAPISIDHASH'";
        } else if (!manualCookie) {
            isValid = false;
            errorMessage = "Please enter the Cookie value";
        }

        return { isValid, errorMessage };
    };

    // Save the manually entered tokens
    const saveManualTokens = async () => {
        const { isValid, errorMessage } = validateTokens();

        if (!isValid) {
            JC_Utils.showToastError(errorMessage);
            return;
        }

        try {
            setIsLoading(true);

            // Save tokens to localStorage
            localStorage.setItem(LocalStorageKeyEnum.JC_YtMusicAuth, manualAuth.trim());
            localStorage.setItem(LocalStorageKeyEnum.JC_YtMusicCookie, manualCookie.trim());

            // Log for debugging
            console.log('Saved authorization token length:', manualAuth.trim().length);
            console.log('Saved cookie length:', manualCookie.trim().length);

            // Success!
            setTokenStatus('success');
            JC_Utils.showToastSuccess("YouTube Music token has been saved successfully!");

            // Clear the input fields
            setManualCookie('');
            setManualAuth('');

            setIsLoading(false);

            // Close the modal
            onClose();

            // Reload the page to apply new tokens
            window.location.reload();

        } catch (error) {
            console.error("Error saving tokens:", error);
            setTokenStatus('error');
            JC_Utils.showToastError("Failed to save tokens. Please try again.");
            setIsLoading(false);
        }
    };

    // - RENDER - //

    return (
        <JC_Modal isOpen={isOpen} onCancel={onClose} title="Reset YouTube Music Token" width="600px">
            <div ref={mainContainerRef} className={styles.mainContainer}>
                {/* Warning Disclaimer */}
                <div className={styles.disclaimer}>
                    <div className={styles.disclaimerText}>
                        <p><strong>Security Notice:</strong> This tool requires access to your YouTube Music session. Your tokens are stored securely and only used for playback/control features. Use at your own discretion.</p>
                        <p><strong>Token Expiration:</strong> These session tokens can expire or be revoked by YouTube. You may need to repeat this process periodically if features stop working.</p>
                    </div>
                </div>

                {/* Instructions */}
                <div className={styles.instructions}>
                    <p>Follow these steps to reset your YouTube Music token:</p>
                    <ol>
                        <li>Click the &quot;Open YouTube Music&quot; button below</li>
                        <li>Sign in to your YouTube Music account in the popup window</li>
                        <li>Open Developer Tools (F12 or right-click → Inspect)</li>
                        <li>Go to the Network tab and type &quot;browse&quot; in the search box</li>
                        <li>On the page, click on any song, album or artist</li>
                        <li>Right click on the &quot;browse?prettyPrint=false&quot; call, select &quot;Copy&quot; → &quot;Copy as fetch (Node.js)&quot;</li>
                        <li>Come back here, click &quot;Paste tokens from Clipboad&quot;</li>
                        <li>Click &quot;Save Tokens&quot;</li>
                    </ol>
                </div>

                {/* Open YouTube Music Button */}
                <div className={styles.resetButtonContainer}>
                    <JC_Button
                        text="Open YouTube Music"
                        onClick={openYouTubeMusic}
                        isDisabled={isLoading}
                        isSecondary
                    />

                    {/* Loading Spinner */}
                    {isLoading && (
                        <div className={styles.spinner}>
                            <Image
                                src="/icons/Refresh.svg"
                                width={30}
                                height={30}
                                alt="Loading"
                            />
                        </div>
                    )}
                </div>

                <div className={styles.curlHelper}>
                    <JC_Button
                        overrideClass={styles.curlButton}
                        text="Paste Tokens From Clipboard"
                        onClick={handlePasteCurl}
                        isSecondary
                    />
                </div>

                <div className={styles.inputGroup}>
                    <JC_Field
                        inputId="authInput"
                        type={FieldTypeEnum.Textarea}
                        label="Authorization:"
                        value={manualAuth}
                        onChange={(value) => setManualAuth(value)}
                        placeholder="Paste Authorization value here (should start with SAPISIDHASH)..."
                        inputOverrideClass={styles.fullWidthInput}
                    />
                </div>

                <div className={styles.inputGroup}>
                    <JC_Field
                        inputId="cookieInput"
                        type={FieldTypeEnum.Textarea}
                        label="Cookie:"
                        value={manualCookie}
                        onChange={(value) => setManualCookie(value)}
                        placeholder="Paste Cookie value here..."
                        inputOverrideClass={styles.fullWidthInput}
                    />
                </div>

                <div className={styles.saveButtonContainer}>
                    <JC_Button
                        text="Save Tokens"
                        onClick={saveManualTokens}
                        isSecondary
                    />
                </div>

                {/* Status Message */}
                {tokenStatus === 'success' && (
                    <div className={`${styles.statusMessage} ${styles.success}`}>
                        Token reset successful!
                    </div>
                )}

                {tokenStatus === 'error' && (
                    <div className={`${styles.statusMessage} ${styles.error}`}>
                        Failed to reset token. Please try again.
                    </div>
                )}
            </div>
        </JC_Modal>
    );
}
