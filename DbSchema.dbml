
// - User - //

Table User {
    Id                      UUID         [not null, PK]
    FirstName               varchar(100) [not null]
    LastName                varchar(100) [    null]
    Email                   varchar(100) [not null]
    PasswordHash            varchar      [not null]
    YtMusicAuthTokenHash    varchar      [not null]
    YtMusicCookieHash       varchar      [not null]
    LoginFailedAttempts     int          [not null, default: 0]
    LoginLockoutDate        timestamp    [    null]
    ChangePasswordToken     varchar(200) [    null]
    ChangePasswordTokenDate timestamp    [    null]
    Phone                   varchar(20)  [    null]
    IsAdmin                 boolean      [not null, default: FALSE, note: 'An admin user can make a user admin through Users list page.']
    IsWholesale             boolean      [not null, default: FALSE, note: 'User has different prices if they are a wholesaler. An admin user can make a user wholesale through the Users list page.']
    CompanyName             varchar(200) [    null,                 note: 'Required field for wholesale users.']
    IsEmailSubscribed       boolean      [not null, default: TRUE]
    IsDiscountUser          boolean      [not null, default: FALSE, note: 'Discount amount set in [GlobalSettings].']
    StripeCustomerId        varchar(100) [    null,                 note: 'Set when user makes payment for first time since their Stripe customer account is created.']
    IsVerified              boolean      [not null, default: FALSE]
    VerificationToken       varchar(200) [    null]
    CreatedAt               timestamp    [not null, default: 'now()']
    ModifiedAt              timestamp    [    null]
    Deleted                 boolean      [not null, default: FALSE]
}


// - User Config - //

Table UserConfig {
    UserId                       UUID         [not null, PK, ref: > User.Id]
    NominatedToListenPlaylistUrl varchar(100) [    null]
    SelectedPlaylistIdListJson   varchar      [    null]
    HiddenPlaylistIdListJson     varchar      [    null]
    NominatedNewSongsPlaylistUrl varchar(100) [    null]
    NewSongsProcessDateLastRun   timestamp    [    null]
    CreatedAt                    timestamp    [not null, default: 'now()']
    ModifiedAt                   timestamp    [    null]
    Deleted                      boolean      [not null, default: FALSE]
}


// - User Artist Subscription - //

Table UserArtistSubscription {
    Id                      UUID         [not null, PK]
    UserId                  UUID         [not null, ref: > User.Id]
    ArtistId                varchar(100) [not null]
    ArtistName              varchar(200) [not null]
    CheckedAlbumIdListJson  varchar      [not null]
    CheckedSingleIdListJson varchar      [not null]
    CreatedAt               timestamp    [not null, default: 'now()']
    ModifiedAt              timestamp    [    null]
    Deleted                 boolean      [not null, default: FALSE]
}


// - User Process Progress - //

Table UserProcessProgress {
    Id                  UUID        [not null, PK]
    UserId              UUID        [not null, PK, ref: > User.Id]
    ProcessCode         varchar(20) [not null]
    DataJson            varchar     [    null]
    ProgressDataJson    varchar     [    null]
    ItemsCompletedJson  varchar     [    null]
    ItemsLeftJson       varchar     [    null]
    CreatedAt           timestamp   [not null, default: 'now()']
    ModifiedAt          timestamp   [    null]
    Deleted             boolean     [not null, default: FALSE]
}


// - Image Cache - //

Table ImageCache {
    Id          UUID         [not null, PK]
    OriginalUrl varchar(500) [not null, unique]
    BlobUrl     varchar(500) [not null]
    Base64      text         [    null]
    CreatedAt   timestamp    [not null, default: 'now()']
}


// - Global Settings - //

Table GlobalSettings {
    Code        varchar(50)  [not null, PK]
    Value       varchar(200) [not null]
    Description varchar(200) [not null]
}